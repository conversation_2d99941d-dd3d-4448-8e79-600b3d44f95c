# LuminariMUD PHP Tools Configuration
# Copy this file to .env and update with your actual values

# Application Environment
# Options: development, testing, production
APP_ENV=production

# Application Timezone
APP_TIMEZONE=UTC

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=luminarimud
DB_USER=your_username
DB_PASS=your_password

# Security Configuration
# Generate a random 32-character string for this
APP_SECRET=your_32_character_secret_key_here

# Session Configuration
SESSION_LIFETIME=3600

# Cache Configuration
CACHE_ENABLED=true
CACHE_TTL=3600

# Logging Configuration
LOG_LEVEL=error
LOG_FILE=logs/application.log

# Authentication Configuration
# Set to true to require authentication for all tools
REQUIRE_AUTH=false

# Admin Configuration
# Default admin credentials (change these!)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=change_this_password

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# File Upload Configuration
MAX_UPLOAD_SIZE=10M
ALLOWED_EXTENSIONS=txt,csv,json

# Email Configuration (for notifications)
MAIL_HOST=localhost
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_FROM=<EMAIL>

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_PATH=backups/
BACKUP_RETENTION_DAYS=30
